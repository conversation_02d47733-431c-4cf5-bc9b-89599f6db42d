"use client"

import { useState } from "react"
import { Link, useNavigate } from "react-router-dom"
import { Eye, EyeOff, Mail, Lock, Loader, AlertCircle } from "lucide-react"
import "./Login.css"
import { signInWithEmailAndPassword } from "firebase/auth"
import { auth } from "../../firebase"
import { useAuth } from "../../AuthContext";


const Login = () => {
  const { setToken, setRole } = useAuth();
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [loginError, setLoginError] = useState(null)
  const [deactivatedError, setDeactivatedError] = useState(false)

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    })
    
    // Clear error when user starts typing
    if (loginError) setLoginError(null);
  }

  // Add this function to handle Firebase auth errors
  const handleFirebaseError = (error) => {
    console.error("Firebase auth error:", error.code, error.message);
    
    // Set appropriate error message based on Firebase error code
    if (error.code === 'auth/invalid-credential' || 
        error.code === 'auth/wrong-password' || 
        error.code === 'auth/user-not-found') {
      setLoginError('Invalid email or password');
    } else if (error.code === 'auth/too-many-requests') {
      setLoginError('Too many failed login attempts. Please try again later.');
    } else {
      setLoginError(error.message || 'Login failed. Please try again.');
    }
    setIsLoading(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoginError(null);
    setDeactivatedError(false);
    setIsLoading(true);
    
    try {
      // Show loading state
      setIsLoading(true);
      
      console.log("Attempting to sign in with:", formData.email);
      
      // Sign in with Firebase - add error handling
      try {
        const userCredential = await signInWithEmailAndPassword(auth, formData.email, formData.password);
        console.log("Firebase sign-in successful");
        
        const user = userCredential.user;
        const idToken = await user.getIdToken();

        // Send token to backend
        console.log("Sending token to backend...");
        const res = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/admin/auth/login`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ token: idToken }),
        });

        if (!res.ok) {
          const errorData = await res.json();
          
          if (errorData.code === "account_deactivated") {
            setDeactivatedError(true);
            throw new Error(errorData.error);
          }
          
          throw new Error(errorData.error || "Login failed");
        }

        const data = await res.json();
        console.log("Backend login successful:", data);
        
        // Store JWT in localStorage and context
        localStorage.setItem("adminToken", data.token);
        localStorage.setItem("adminRole", data.role);
        setToken(data.token);
        setRole(data.role);

        // Force a small delay to ensure state is updated
        setTimeout(() => {
          // Navigate to dashboard
          console.log("Navigating to dashboard...");
          navigate("/dashboard");
        }, 100);
      } catch (firebaseError) {
        console.error("Firebase authentication error:", firebaseError);
        handleFirebaseError(firebaseError);
        return; // Stop execution if Firebase auth fails
      }
      
      // Rest of your code...
    } catch (err) {
      console.error("Login failed", err);
      setLoginError("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="modern-auth-container">
      <div className="modern-auth-card">
        <div className="auth-header">
          <div className="logo-section">
            <div className="logo-icon">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <circle cx="20" cy="20" r="20" fill="url(#gradient)" />
                <path d="M15 20L18 23L25 16" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#cc65e8" />
                    <stop offset="100%" stopColor="#a855f7" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <h1 className="auth-title">
              Welcome back to <span className="brand-gradient">Soñar</span>
            </h1>
            <p className="auth-subtitle">Sign in to your admin dashboard</p>
          </div>
        </div>

        <div className="modern-auth-form">
          <form onSubmit={handleSubmit} className="login-form">
            <div className="modern-form-group">
              <label className="modern-label">Email Address</label>
              <div className="modern-input-wrapper">
                <div className="input-icon">
                  <Mail size={18} />
                </div>
                <input
                  type="email"
                  name="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                  className="modern-input"
                  required
                />
              </div>
            </div>

            <div className="modern-form-group">
              <label className="modern-label">Password</label>
              <div className="modern-input-wrapper">
                <div className="input-icon">
                  <Lock size={18} />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  placeholder="••••••••"
                  value={formData.password}
                  onChange={handleChange}
                  className="modern-input"
                  required
                />
                <button
                  type="button"
                  className="modern-password-toggle"
                  onClick={(e) => {
                    e.preventDefault();
                    setShowPassword(!showPassword);
                  }}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>

            <div className="modern-form-options">
              <label className="modern-checkbox">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                />
                <span className="checkmark"></span>
                <span className="checkbox-text">Remember me</span>
              </label>

              <Link to="/forgot-password" className="forgot-link">
                Forgot password?
              </Link>
            </div>

            <button
              type="submit"
              className={`modern-auth-button ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="button-loading">
                  <Loader size={20} className="spinner" />
                  <span>Signing in...</span>
                </div>
              ) : (
                <span>Sign In</span>
              )}
            </button>

            {(loginError || deactivatedError) && (
              <div className="modern-error-message">
                <AlertCircle size={16} />
                <span>
                  {deactivatedError
                    ? "Your account has been deactivated. Please contact an administrator."
                    : loginError
                  }
                </span>
              </div>
            )}
          </form>
        </div>
      </div>

      {/* Background decoration */}
      <div className="auth-background">
        <div className="floating-shape shape-1"></div>
        <div className="floating-shape shape-2"></div>
        <div className="floating-shape shape-3"></div>
      </div>
    </div>
  )
}

export default Login
