/* App container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* App header - fixed at top */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.app-logo {
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  color: white;
}

.app-logo strong {
  font-weight: 800;
  color: white;
}

.app-logo-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.brand-gradient {
  background: linear-gradient(135deg, #cc65e8 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

/* Dashboard layout */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  padding-top: 80px;
  background-color: #fff;
  width: 100%;
}

/* Sidebar styles */
.sidebar {
  width: 240px;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  padding: 25px 0;
  border-right: 1px solid #eee;
  height: calc(100vh - 80px);
  position: fixed;
  top: 80px;
  justify-content: space-between;
}

.sidebar-nav {
  width: 100%;
  flex: 1;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

.sidebar-nav li {
  width: 100%;
  cursor: pointer;
  margin-bottom: 5px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 15px 25px;
  color: #666;
  text-decoration: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar-nav li:hover .nav-item {
  color: #c066e8;
}

.sidebar-nav li.active .nav-item {
  background-color: #f8e6ff;
  color: #c066e8;
  border-left: 4px solid #c066e8;
}

/* Sidebar nav icons */
.nav-icon {
  color: #666;
  margin-right: 12px;
}

.sidebar-nav li.active .nav-icon {
  color: #c066e8;
}

.sidebar-nav li:hover:not(.active) .nav-icon {
  color: #999;
}

/* Top nav actions */
.top-nav-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: white;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
}

.logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.logout-button svg {
  width: 16px;
  height: 16px;
}

/* User profile at bottom of sidebar */
.user-profile-bottom {
  padding: 15px 25px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 12px;
  background-color: #c066e8;
  color: white;
}

.user-info-bottom {
  overflow: hidden;
}

.user-name {
  margin: 0;
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  margin: 0;
  font-size: 10px;
  color: #777;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Main content area */
.main-content {
  flex: 1;
  padding: 25px;
  margin-left: 240px;
  width: calc(100% - 240px);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

/* Add CSS variables for consistent colors */
:root {
  --primary-purple: #c066e8;
  --light-purple: #f8e6ff;
  --dark-purple: #a94fd0;
  --chart-blue: #4285F4;
  --chart-yellow: #FBBC05;
  --chart-red: #EA4335;
  --text-dark: #333333;
  --text-medium: #666666;
  --text-light: #999999;
  --bg-white: #ffffff;
  --bg-light: #f9f9f9;
  --border-light: #f0f0f0;
}

