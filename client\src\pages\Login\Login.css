
/* Modern Auth Container */
.modern-auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* Modern Auth Card */
.modern-auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  width: 100%;
  max-width: 440px;
  overflow: hidden;
  position: relative;
  z-index: 10;
  animation: slideUp 0.6s ease-out;
}

/* Auth Header */
.auth-header {
  padding: 40px 32px 20px;
  text-align: center;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  margin-bottom: 8px;
  animation: float 3s ease-in-out infinite;
}

.auth-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
}

.brand-gradient {
  background: linear-gradient(135deg, #cc65e8 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.auth-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

/* Modern Form */
.modern-auth-form {
  padding: 0 32px 40px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Modern Google Button */
.modern-google-button {
  width: 100%;
  padding: 16px 24px;
  background: #ffffff;
  color: #374151;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 56px;
  position: relative;
  overflow: hidden;
}

.modern-google-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(204, 101, 232, 0.1), transparent);
  transition: left 0.5s;
}

.modern-google-button:hover::before {
  left: 100%;
}

.modern-google-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #cc65e8;
}

.modern-google-button:active {
  transform: translateY(0);
}

.modern-google-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.modern-google-button:disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: #e5e7eb;
}

.google-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Button Loading State */
.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Modern Error Message */
.modern-error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #fef2f2;
  color: #dc2626;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #fecaca;
  font-size: 14px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

.modern-error-message svg {
  flex-shrink: 0;
}

/* Auth Footer */
.auth-footer {
  text-align: center;
}

.auth-note {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
  line-height: 1.4;
}

/* Background Decoration */
.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .modern-auth-container {
    padding: 16px;
  }

  .modern-auth-card {
    max-width: 100%;
    border-radius: 16px;
  }

  .auth-header {
    padding: 32px 24px 16px;
  }

  .modern-auth-form {
    padding: 0 24px 32px;
  }

  .auth-title {
    font-size: 24px;
  }
}
